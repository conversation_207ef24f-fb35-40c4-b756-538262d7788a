# Functional Activations Save/Load Guide

This guide explains how to save and reuse functional activations from the MASK benchmark steering pipeline for use in other scripts.

## Overview

The `production_mask_steering.py` script now automatically saves functional activations that can be loaded and reused in other scripts. This is useful for:

- Applying steering to new text inputs without recomputing activations
- Experimenting with different steering parameters
- Building applications that use pre-trained steering vectors
- Sharing steering configurations between team members

## What Gets Saved

When you run the pipeline, two files are automatically saved:

1. **Functional Activations File** (`functional_activations_YYYYMMDD_HHMMSS.pt`):
   - Mutual information results
   - Scheming and truthful feature indices
   - z_S and z_T steering vectors
   - All metadata needed for reconstruction

2. **Configuration File** (`steering_config_YYYYMMDD_HHMMSS.json`):
   - Model name and SAE path
   - Training parameters used
   - Path to the corresponding activations file
   - Timestamp and other metadata

## Saving Functional Activations

### Automatic Saving (Recommended)

Simply run the pipeline as normal:

```bash
python production_mask_steering.py run_pipeline \
    --model_name="your-model" \
    --sae_path="/path/to/sae"
```

The script will automatically save both files to the `./results/` directory.

### Manual Saving

You can also save activations manually using the ITAS steering class:

```python
from itas.core.steering import KnowledgeSelectionSteering

# After creating functional_activations
steering.save_results(
    save_path="my_activations.pt",
    mi_result=mi_result,
    functional_activations=functional_activations,
)
```

## Loading Functional Activations

### Method 1: Using the Main Script

```bash
python production_mask_steering.py load_activations \
    --activations_path="./results/functional_activations_20241201_123456.pt" \
    --sae_path="/path/to/sae"
```

### Method 2: Using the Example Script

```bash
python load_functional_activations_example.py demo \
    --activations_path="./results/functional_activations_20241201_123456.pt" \
    --config_path="./results/steering_config_20241201_123456.json"
```

### Method 3: In Your Own Python Script

```python
import json
from itas.core.sae import SAE
from itas.core.steering import KnowledgeSelectionSteering

# Load configuration
with open("steering_config_20241201_123456.json", 'r') as f:
    config = json.load(f)

# Load SAE
sae = SAE.load(config["sae_path"], device="cuda")

# Load functional activations
steering, mi_result, functional_activations = KnowledgeSelectionSteering.load_results(
    load_path="functional_activations_20241201_123456.pt",
    sae=sae,
    device="cuda",
)

# Now you can use the functional activations for steering
print(f"Loaded {len(functional_activations.scheming_indices)} scheming features")
print(f"Loaded {len(functional_activations.truthful_indices)} truthful features")
```

## Using Loaded Activations

Once loaded, you can use the functional activations for steering:

```python
# Apply steering to hidden states
steered_hiddens = steering.apply_knowledge_steering(
    hidden_states=original_hiddens,
    steering_direction='truthful',  # or 'scheming'
    alpha=1.5,  # steering strength
    functional_activations=functional_activations,
)

# Create intervention function for model hooks
intervention_fn = steering.create_intervention_function(
    steering_direction='truthful',
    alpha=1.5,
    functional_activations=functional_activations,
)
```

## File Structure

After running the pipeline, your results directory will look like:

```
results/
├── functional_activations_20241201_123456.pt  # Main activations file
├── steering_config_20241201_123456.json       # Configuration
└── mask_steering_20241201_123456.log          # Log file
```

## What's Included in the Saved Data

The `.pt` file contains:

- `mi_scores`: Mutual information scores for each SAE feature
- `expectation`: Expected value differences (E_S[Z_i] - E_T[Z_i])
- `scheming_features`: Indices of scheming-correlated features
- `truthful_features`: Indices of truthful-correlated features
- `top_k_features`: Top-k features by mutual information
- `z_scheming`: Functional activation vector for scheming steering
- `z_truthful`: Functional activation vector for truthful steering
- `scheming_indices`: Feature indices used in z_scheming
- `truthful_indices`: Feature indices used in z_truthful
- Metadata for both MI calculation and functional activations

## Best Practices

1. **Keep Configuration Files**: Always save the JSON config alongside the .pt file
2. **Version Control**: Include timestamps in filenames for easy tracking
3. **Documentation**: Record the training data and parameters used
4. **Validation**: Test loaded activations on known examples before production use
5. **Backup**: Keep copies of successful steering configurations

## Troubleshooting

### Common Issues

1. **Device Mismatch**: Ensure the device matches between saving and loading
2. **SAE Path**: Make sure the SAE path in the config is still valid
3. **ITAS Version**: Ensure compatible ITAS module versions
4. **Memory**: Large activations may require sufficient GPU/CPU memory

### Error Messages

- `"No mutual information result to save"`: Run MI calculation first
- `"SAE must have either 'pre_acts' or 'encode' method"`: Check SAE compatibility
- `"No functional activations available"`: Load or create activations first

## Example Use Cases

1. **Interactive Steering**: Load activations and apply to user inputs
2. **Batch Processing**: Apply steering to large datasets
3. **A/B Testing**: Compare different steering configurations
4. **Research**: Analyze feature importance across different models
5. **Production**: Deploy steering in applications without retraining

## Performance Notes

- Loading is much faster than recomputing (seconds vs minutes/hours)
- Functional activations are typically small (few MB) compared to full model weights
- GPU memory usage is minimal for the activations themselves
- Most computation happens during the original MI calculation phase
