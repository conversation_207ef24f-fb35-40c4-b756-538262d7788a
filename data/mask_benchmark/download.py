import os
from datasets import load_dataset
import pandas as pd

for split in [
    "continuations",
    "disinformation",
    "doubling_down_known_facts",
    "known_facts",
    "provided_facts",
    "statistics",
]:
    ds = load_dataset("cais/mask", split)

    # Create directories if they don't exist
    os.makedirs("./csv_data", exist_ok=True)
    os.makedirs("./test_csv_data", exist_ok=True)

    df = pd.DataFrame(ds["test"])
    df.to_csv(f"./csv_data/{split}.csv", index=False)

    # Create test data by sampling 20% of the training data
    df.sample(frac=0.2).to_csv(f"./test_csv_data/{split}.csv", index=False)
