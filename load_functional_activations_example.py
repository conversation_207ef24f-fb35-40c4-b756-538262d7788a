#!/usr/bin/env python3
"""
Example script showing how to load and use saved functional activations.

This script demonstrates how to:
1. <PERSON><PERSON> saved functional activations from production_mask_steering.py
2. Use them for steering in a new context
3. Apply steering to new text inputs

Usage:
    python load_functional_activations_example.py \
        --activations_path="./results/functional_activations_20241201_123456.pt" \
        --config_path="./results/steering_config_20241201_123456.json"
"""

import os
import json
import logging
import torch
import fire
from typing import Optional, List, Dict, Any

# Import ITAS components
from itas.core.model_loader import UniversalModelLoader
from itas.core.config import ModelConfig
from itas.core.sae import SAE
from itas.core.steering import KnowledgeSelectionSteering

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def load_functional_activations(
    activations_path: str,
    config_path: str,
    device: str = "auto",
) -> tuple:
    """
    Load saved functional activations and configuration.
    
    Args:
        activations_path: Path to saved functional activations (.pt file)
        config_path: Path to saved configuration (.json file)
        device: Device to load on
        
    Returns:
        Tuple of (steering, mi_result, functional_activations, config)
    """
    logger.info(f"Loading functional activations from {activations_path}")
    logger.info(f"Loading configuration from {config_path}")
    
    # Setup device
    if device == "auto":
        device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"Using device: {device}")
    
    # Load configuration
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # Load model and SAE
    logger.info("Loading model and SAE...")
    model_config = ModelConfig(model_name=config["model_name"], load_for_generation=True)
    model_loader = UniversalModelLoader(model_config)
    model, tokenizer = model_loader.load_model_and_tokenizer()
    
    sae = SAE.load(config["sae_path"], device=device)
    
    # Load functional activations using the class method
    steering, mi_result, functional_activations = KnowledgeSelectionSteering.load_results(
        load_path=activations_path,
        sae=sae,
        device=device,
        num_processes=1,  # Use single process for simplicity
    )
    
    logger.info("✅ Successfully loaded functional activations!")
    logger.info(f"   Scheming features: {len(functional_activations.scheming_indices)}")
    logger.info(f"   Truthful features: {len(functional_activations.truthful_indices)}")
    logger.info(f"   z_S norm: {functional_activations.z_scheming.norm().item():.4f}")
    logger.info(f"   z_T norm: {functional_activations.z_truthful.norm().item():.4f}")
    
    return steering, mi_result, functional_activations, config, model, tokenizer


def apply_steering_to_text(
    text: str,
    steering: KnowledgeSelectionSteering,
    functional_activations,
    model,
    tokenizer,
    steering_direction: str = "truthful",
    alpha: float = 1.0,
    target_layer: int = 16,
    max_new_tokens: int = 100,
    device: str = "cuda",
) -> Dict[str, str]:
    """
    Apply steering to a text input and generate responses.
    
    Args:
        text: Input text to steer
        steering: KnowledgeSelectionSteering instance
        functional_activations: Loaded functional activations
        model: Language model
        tokenizer: Model tokenizer
        steering_direction: 'truthful' or 'scheming'
        alpha: Steering strength
        target_layer: Layer to apply steering to
        max_new_tokens: Max tokens to generate
        device: Device for computation
        
    Returns:
        Dictionary with original and steered responses
    """
    logger.info(f"Applying {steering_direction} steering to text: {text[:50]}...")
    
    # Tokenize input
    inputs = tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
    input_ids = inputs["input_ids"].to(device)
    
    # Generate original response
    with torch.no_grad():
        original_outputs = model.generate(
            input_ids,
            max_new_tokens=max_new_tokens,
            temperature=0.7,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id,
        )
    
    original_response = tokenizer.decode(
        original_outputs[0][input_ids.shape[1]:], 
        skip_special_tokens=True
    ).strip()
    
    # TODO: Apply steering during generation
    # This would require implementing proper hook integration during generation
    # For now, we'll just show how the functional activations can be accessed
    
    logger.info("Note: Full steering integration during generation requires hook setup")
    logger.info(f"Available steering directions: {steering_direction}")
    logger.info(f"Functional activations ready for use with alpha={alpha}")
    
    return {
        "input": text,
        "original_response": original_response,
        "steered_response": f"[Steering with {steering_direction} direction would be applied here]",
        "steering_info": {
            "direction": steering_direction,
            "alpha": alpha,
            "target_layer": target_layer,
            "scheming_features": len(functional_activations.scheming_indices),
            "truthful_features": len(functional_activations.truthful_indices),
        }
    }


def demo_steering(
    activations_path: str,
    config_path: str,
    test_texts: Optional[List[str]] = None,
    steering_direction: str = "truthful",
    alpha: float = 1.0,
    device: str = "auto",
):
    """
    Demonstrate loading and using functional activations.
    
    Args:
        activations_path: Path to saved functional activations
        config_path: Path to saved configuration
        test_texts: List of texts to test (uses defaults if None)
        steering_direction: Direction to steer ('truthful' or 'scheming')
        alpha: Steering strength
        device: Device to use
    """
    # Load functional activations
    steering, mi_result, functional_activations, config, model, tokenizer = load_functional_activations(
        activations_path=activations_path,
        config_path=config_path,
        device=device,
    )
    
    # Default test texts if none provided
    if test_texts is None:
        test_texts = [
            "What is the capital of France?",
            "Is climate change real?",
            "Should I trust this financial advice?",
        ]
    
    logger.info(f"Testing steering on {len(test_texts)} examples...")
    
    # Apply steering to each test text
    results = []
    for i, text in enumerate(test_texts):
        logger.info(f"\n--- Example {i+1}/{len(test_texts)} ---")
        
        result = apply_steering_to_text(
            text=text,
            steering=steering,
            functional_activations=functional_activations,
            model=model,
            tokenizer=tokenizer,
            steering_direction=steering_direction,
            alpha=alpha,
            target_layer=config["target_layer"],
            device=device,
        )
        
        results.append(result)
        
        # Display result
        print(f"Input: {result['input']}")
        print(f"Original: {result['original_response']}")
        print(f"Steered ({steering_direction}): {result['steered_response']}")
        print(f"Steering info: {result['steering_info']}")
    
    logger.info("✅ Demo completed!")
    return results


if __name__ == "__main__":
    fire.Fire({
        "demo": demo_steering,
        "load": load_functional_activations,
    })
